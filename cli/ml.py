#!/usr/bin/env python3
"""Command-line interface for MyProject."""
from datetime import datetime
import sys
import logging
import click

from utils.logging import setup_logging
from config import settings

logger = logging.getLogger(__name__)


@click.group()
@click.pass_context
def cli(ctx):
    ctx.ensure_object(dict)    
    # Setup logging
    setup_logging(log_file_name="cli.log", log_level=settings().env('LOG_LEVEL', 'INFO'))


# Machine learning model commands group
@cli.group()
def elastic_price():
    """Machine learning model commands."""
    pass

# python3 -m cli.ml elastic-price train  2>&1|tee ./logs/console_output.log
@elastic_price.command("train")
def train():
    """learn model for model training."""

    import time
    # from lib.cron import is_now_between_midnight_and_one
    from datetime import datetime, timezone
    from facades.redis.redisdb import RedisFacade   
    if RedisFacade.instance().db_query(1).exists("cli.ml__elastic-price__train") :
        print('sleep(60)')
        time.sleep(60)
        return

    RedisFacade.instance().db_query(1).set("cli.ml__elastic-price__train", "1", 60*60)

    # if not is_now_between_midnight_and_one(datetime.now(timezone.utc)):
    #     print("The current time is not between midnight and 1 AM.")
    #     return

    key = "cli.ml__elastic-price__train__ModelEp2Trainer"
    if not RedisFacade.instance().db_query(1).exists(key) :
        print("Training models...")
        try:
            from domains.elasticprice.model_ep2 import ModelEp2Trainer
            trainer = ModelEp2Trainer()
            trainer.train()
        except Exception as e:
            print(f"Error training 2 model: {str(e)}")


        # try:
        #     from domains.elasticprice.enhanced_model_training import main_train
        #     main_train()
        # except Exception as e:
        #     print(f"Error training 3 model: {str(e)}")

        RedisFacade.instance().db_query(1).set(key, "1", 6*60*60)
    # if

    from domains.elasticprice.model_recomendation import main
    main()

    print("Training completed.")
#def


# python3 -m cli.ml elastic-price retrain --model=ep2 --new-model=1 --snapshot-days=3 2>&1|tee ./logs/console_output.log
@elastic_price.command("retrain") 
@click.option("--model", required=True, default="ep2", type=str, help="Model name. Options: ep1, ep2")
@click.option("--new-model", required=False, default=0, type=int, help="Force new model")
@click.option("--snapshot-days", required=False, default=100, type=int, help="Number of days to snapshot")
@click.option("--data-bulk-size", required=False, default=1000000, type=int, help="Number of data points to load in bulk")
def retrain(model, new_model, snapshot_days, data_bulk_size):
    """learn model for model training."""


    from domains.elasticprice.enhanced_model_training import main_train
    main_train()

    if model == "ep2":
        from domains.elasticprice.model_ep2 import ModelEp2Trainer
        trainer = ModelEp2Trainer()
        trainer.train(force_new_model=bool(new_model), snapshot_days=snapshot_days, data_bulk_size=data_bulk_size)
    else:
        print("Invalid model name")
#def


# python3 -m cli.ml elastic-price test-train --new-model=1 --snapshot-days=3 2>&1|tee ./logs/test_train_output.log
@elastic_price.command("test-train") 
@click.option("--new-model", required=False, default=0, type=int, help="Force new model")
@click.option("--snapshot-days", required=False, default=2, type=int, help="Number of days to snapshot")
@click.option("--data-bulk-size", required=False, default=1000000, type=int, help="Number of data points to load in bulk")
def test_train(new_model, snapshot_days, data_bulk_size):
    """learn model for model training."""

    # from domains.elasticprice.sales_history import SalesHistory
    # from datetime import datetime, timedelta
    
    # sh = SalesHistory.new_instance()
    # sh.make_snapshot(19, datetime.now() - timedelta(days=100), datetime.now())


    from domains.elasticprice.model_recomendation import main, generate_recommendations_for_customer
    # main()
    generate_recommendations_for_customer(12432, 10)

    # from domains.repository.eprice_repository import ElasticPriceRepository

    # repo = ElasticPriceRepository.new_instance()
    # customer_id = 19
    # repo.remove_old_recommendations(customer_id, 5)

    # from domains.elasticprice.model_ep2 import ModelEp2Trainer #, load_last_trained_ep3_model, predict, result_from_predicted
    # trainer = ModelEp2Trainer()
    # trainer.train(force_new_model=True, snapshot_days=3)
    # # trainer.train_from_csv('train_data_ep3_20250624134405.csv', force_new_model=True, save_to_file_only=True)

    # from domains.repository.product_repository import ProductRepository
    # repo = ProductRepository.new_instance()
    # items = repo.get_product_optimization_by_strategy(12640, ['elasticPricing'], 1000, 0)
    # print(items)
    # print(len(items))

#def




def main():
    """Main entry point for the CLI application."""
    try:
        cli(obj={})
        return 0
    except Exception as e:
        logger.error(f"Error executing command: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())


# # If installed as a package
# myproject learn load-data --customer-id=19

# # If running directly from the script
# python -m cli.main learn load-data --customer-id=19
