import sys
import os
import warnings
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from datetime import datetime, timedelta

# Add project root to path to import from project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from domains.elasticprice.loader import Loader
from domains.repository.eprice_repository import ElasticPriceRepository

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suppress warnings
warnings.filterwarnings('ignore')

@dataclass
class ModelConfig:
    """Configuration class for the price prediction model"""
    # Data parameters
    # DATA_FILE: str = 'sales_history10.csv'
    MAX_ROWS: int = None
    MIN_PRODUCT_HISTORY: int = 3

    # Feature engineering parameters
    ROLLING_WINDOW: int = 3
    # MAX_PRICE_STEP_RATIO: float = 0.1
    # MIN_PRICE_STEP: float = 0.01

    # Model parameters
    HIDDEN_LAYERS: List[int] = None
    LEARNING_RATE: float = 0.001
    MAX_EPOCHS: int = 1000
    PATIENCE: int = 50
    EARLY_STOP_LOSS: float = 0.01

    # Prediction thresholds
    RAISE_THRESHOLD: float = 0.6
    LOWER_THRESHOLD: float = 0.4

    # Business rules
    MAX_DAYS_FOR_PRICE_RAISE: int = 1  # Do not raise price if last order was more than MAX_DAYS_FOR_PRICE_RAISE days ago

    # Random state for reproducibility
    RANDOM_STATE: int = 42

    def __post_init__(self):
        if self.HIDDEN_LAYERS is None:
            self.HIDDEN_LAYERS = [32, 16, 8]


class PricePredictionModel:
    """Enhanced price prediction model with better architecture and validation"""

    def __init__(self, config: ModelConfig = None):
        self.config = config or ModelConfig()
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.model = None
        self.feature_names = []
        self.training_history = []

    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Load and preprocess the sales data"""
        try:
            logger.info(f"Loaded {len(data)} rows of data")

            # Basic data validation
            required_columns = ['asin', 'sku', 'order_marketplace_id', 'order_purchase_date',
                              'total_quantity', 'total_price', 'customer_id']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")

            # ensure order_purchase_date is datetime
            data['order_purchase_date'] = pd.to_datetime(data['order_purchase_date'], format='%Y-%m-%d')

            # Sort data
            data = data.sort_values(['asin', 'sku', 'order_marketplace_id', 'order_purchase_date'])

            # Limit data size if specified
            if self.config.MAX_ROWS and len(data) > self.config.MAX_ROWS:
                data = data.head(self.config.MAX_ROWS)
                logger.info(f"Limited data to {self.config.MAX_ROWS} rows")

            # Create product identifier
            data['product_id'] = (data['asin'] + '|' + data['sku'] + '|' +
                                data['order_marketplace_id'] + '|' +
                                data['customer_id'].astype(str))

            # Price preprocessing
            data['total_price'] = data['total_price'] / 100  # Convert to dollars
            data['unit_price'] = data['total_price'] / data['total_quantity']

            # Remove invalid data
            data = data[data['total_quantity'] > 0]
            data = data[data['total_price'] > 0]
            data = data.dropna(subset=['unit_price'])

            logger.info(f"Preprocessed data: {len(data)} rows, {data['product_id'].nunique()} unique products")
            return data

        except Exception as e:
            logger.error(f"Error preprocessing data: {str(e)}")
            raise


    def calculate_target(self, row) -> int:
        """Calculate target variable: 1 for demand increase, 0 for decrease/hold"""
        # Improved target calculation with more sophisticated logic
        recent_sale = row['last_day_sold'] <= 2
        quantity_increase = row['total_quantity'] >= row['total_quantity_previous']
        above_average = row['total_quantity'] >= row['quantity_ma']

        # Target is 1 if recent sale AND (quantity increased OR above moving average)
        if recent_sale and (quantity_increase or above_average):
            return 1
        return 0

    # def calculate_step_price(self, row) -> float:
    #     """Calculate recommended price step size"""
    #     if pd.isna(row['unit_price_previous']):
    #         return self.config.MIN_PRICE_STEP

    #     delta_price = abs(row['unit_price'] - row['unit_price_previous'])
    #     max_step_price = row['unit_price'] * self.config.MAX_PRICE_STEP_RATIO

    #     step_price = max(delta_price, self.config.MIN_PRICE_STEP)
    #     step_price = min(step_price, max_step_price)

    #     return step_price

    def engineer_features(self, df_group: pd.DataFrame) -> pd.DataFrame:
        """Engineer features for a product group"""
        df = df_group.copy()
        df = df.sort_values('order_purchase_date').reset_index(drop=True)

        # Lag features
        df['total_price_previous'] = df['total_price'].shift(1)
        df['unit_price_previous'] = df['unit_price'].shift(1)
        df['total_quantity_previous'] = df['total_quantity'].shift(1)

        # Rolling statistics
        df['quantity_ma'] = df['total_quantity'].rolling(
            window=self.config.ROLLING_WINDOW, min_periods=1
        ).mean()
        df['price_ma'] = df['unit_price'].rolling(
            window=self.config.ROLLING_WINDOW, min_periods=1
        ).mean()
        df['quantity_std'] = df['total_quantity'].rolling(
            window=self.config.ROLLING_WINDOW, min_periods=1
        ).std().fillna(0)

        # Time-based features
        df['last_day_sold'] = df['order_purchase_date'].diff().dt.days.fillna(0)
        df['day_of_week'] = df['order_purchase_date'].dt.dayofweek
        df['month'] = df['order_purchase_date'].dt.month

        # Price and demand changes
        df['price_change'] = df['total_price'].pct_change().fillna(0)
        df['demand_change'] = df['total_quantity'].pct_change().fillna(0)

        # Price elasticity of demand (handle division by zero)
        df['PED'] = np.where(
            df['price_change'] != 0,
            df['demand_change'] / df['price_change'],
            0
        )
        df['PED'] = df['PED'].replace([np.inf, -np.inf], 0)

        # Price step calculation
        # df['price_step'] = df.apply(self.calculate_step_price, axis=1)

        # Target variable
        df['target'] = df.apply(self.calculate_target, axis=1)

        # Additional features
        df['price_volatility'] = df['unit_price'].rolling(
            window=self.config.ROLLING_WINDOW, min_periods=1
        ).std().fillna(0)

        df['demand_trend'] = df['total_quantity'].rolling(
            window=self.config.ROLLING_WINDOW, min_periods=1
        ).apply(lambda x: 1 if len(x) > 1 and x.iloc[-1] > x.iloc[0] else 0, raw=False).fillna(0)

        return df

    def create_neural_network(self, input_size: int) -> nn.Module:
        """Create an improved neural network architecture"""
        layers = []
        prev_size = input_size

        # Add hidden layers
        for hidden_size in self.config.HIDDEN_LAYERS:
            layers.extend([
                nn.Linear(prev_size, hidden_size),
                nn.BatchNorm1d(hidden_size),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_size = hidden_size

        # Output layer
        layers.extend([
            nn.Linear(prev_size, 1),
            nn.Sigmoid()
        ])

        return nn.Sequential(*layers)

    def train_model(self, X: np.ndarray, y: np.ndarray) -> Dict:
        """Train the neural network model with proper validation"""
        # Convert to tensors
        X_tensor = torch.tensor(X, dtype=torch.float32)
        y_tensor = torch.tensor(y, dtype=torch.float32).unsqueeze(1)

        # Split data for validation
        if len(X) > 10:  # Only split if we have enough data
            X_train, X_val, y_train, y_val = train_test_split(
                X_tensor, y_tensor, test_size=0.2, random_state=self.config.RANDOM_STATE
            )
        else:
            X_train, X_val, y_train, y_val = X_tensor, X_tensor, y_tensor, y_tensor

        # Create model
        self.model = self.create_neural_network(X.shape[1])
        criterion = nn.BCELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=self.config.LEARNING_RATE)

        # Training loop with early stopping
        best_loss = float('inf')
        patience_counter = 0
        training_losses = []
        validation_losses = []

        for epoch in range(self.config.MAX_EPOCHS):
            # Training
            self.model.train()
            optimizer.zero_grad()
            train_outputs = self.model(X_train)
            train_loss = criterion(train_outputs, y_train)
            train_loss.backward()
            optimizer.step()

            # Validation
            self.model.eval()
            with torch.no_grad():
                val_outputs = self.model(X_val)
                val_loss = criterion(val_outputs, y_val)

            training_losses.append(train_loss.item())
            validation_losses.append(val_loss.item())

            # Early stopping logic
            if val_loss.item() < best_loss:
                best_loss = val_loss.item()
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= self.config.PATIENCE or best_loss < self.config.EARLY_STOP_LOSS:
                logger.info(f"Early stopping at epoch {epoch}, best loss: {best_loss:.6f}")
                break

            if epoch % 100 == 0:
                logger.info(f"Epoch {epoch}, Train Loss: {train_loss.item():.6f}, Val Loss: {val_loss.item():.6f}")

        return {
            'final_loss': best_loss,
            'epochs_trained': epoch + 1,
            'training_losses': training_losses,
            'validation_losses': validation_losses
        }

    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions using the trained model"""
        if self.model is None:
            raise ValueError("Model not trained yet")

        self.model.eval()
        with torch.no_grad():
            X_tensor = torch.tensor(X, dtype=torch.float32)
            predictions = self.model(X_tensor).numpy()

        return predictions.flatten()

    def get_feature_names(self) -> List[str]:
        """Get the list of feature names used by the model"""
        return [
            'unit_price', 'total_quantity', 'quantity_ma', 'price_ma', 'quantity_std',
            'last_day_sold', 'day_of_week', 'month', 'price_change', 'demand_change',
            'PED', 'price_volatility', 'demand_trend'
        ]

    def process_product_group(self, df_group: pd.DataFrame, product_id: str, reference_date: Optional[datetime] = None) -> Optional[Dict[str, Any]]:
        """Process a single product group and return recommendation"""

        logger.info(f"Processing {product_id}")
        try:
            # Skip if insufficient data
            if len(df_group) < self.config.MIN_PRODUCT_HISTORY:
                logger.info(f"Skipping {product_id}: insufficient data ({len(df_group)} rows)")
                return None

            # Engineer features
            df_processed = self.engineer_features(df_group)
            df_processed = df_processed.dropna()

            if len(df_processed) < self.config.MIN_PRODUCT_HISTORY:
                logger.info(f"Skipping {product_id}: insufficient data after preprocessing")
                return None

            # Prepare features
            feature_names = self.get_feature_names()
            X = df_processed[feature_names].values
            y = df_processed['target'].values

            # Scale features
            X_scaled = self.scaler.fit_transform(X)

            # Train model
            training_results = self.train_model(X_scaled, y)

            # Make prediction on latest data
            latest_features = X_scaled[-1].reshape(1, -1)
            prediction = self.predict(latest_features)[0]

            # Determine action
            latest_row = df_processed.iloc[-1]

            # Calculate days since last order from reference date or current date
            if reference_date is not None:
                current_date = reference_date.date()
            else:
                current_date = datetime.now().date()

            last_order_date = latest_row['order_purchase_date'].date()
            days_since_last_order = (current_date - last_order_date).days

            # For testing purposes, if we're using historical data and no reference_date provided,
            # calculate from the latest date in the dataset
            if reference_date is None and days_since_last_order > 365:  # If more than a year, likely using test data
                max_date_in_data = df_processed['order_purchase_date'].max().date()
                days_since_last_order = (max_date_in_data - last_order_date).days

            if prediction > self.config.RAISE_THRESHOLD:
                action = "RAISE_PRICE"
            elif prediction < self.config.LOWER_THRESHOLD:
                action = "LOWER_PRICE"
            else:
                action = "HOLD_PRICE"


            # Check if business rule was applied
            business_rule_applied = None

            # Business rule: Do not raise price if last order was more than MAX_DAYS_FOR_PRICE_RAISE days ago
            if action == "RAISE_PRICE" and days_since_last_order > self.config.MAX_DAYS_FOR_PRICE_RAISE:
                business_rule_applied = f"Do not raise price if last order was more than {self.config.MAX_DAYS_FOR_PRICE_RAISE} days ago"
                action = "HOLD_PRICE"
                logger.info(f"Product {product_id}: Overriding RAISE_PRICE to HOLD_PRICE due to last order being {days_since_last_order} days ago (max allowed: {self.config.MAX_DAYS_FOR_PRICE_RAISE})")                

            # if days_since_last_order > self.config.MAX_DAYS_FOR_PRICE_RAISE *3:
            #     action = "LOWER_PRICE"
            #     business_rule_applied = f"Lower price if last order was more than {self.config.MAX_DAYS_FOR_PRICE_RAISE *3} days ago"
            #     logger.info(f"Product {product_id}: Overriding to LOWER_PRICE due to last order being {days_since_last_order} days ago (max allowed: {self.config.MAX_DAYS_FOR_PRICE_RAISE *3})")

            # Prepare recommendation
            # Convert numpy types to native Python types for JSON serialization
            def convert_to_python_type(value):
                """Convert numpy/pandas types to native Python types"""
                if hasattr(value, 'item'):  # numpy scalar
                    return value.item()
                elif isinstance(value, (np.integer, np.int64, np.int32)):
                    return int(value)
                elif isinstance(value, (np.floating, np.float64, np.float32)):
                    return float(value)
                elif pd.isna(value):
                    return None
                else:
                    return value

            recommendation = {
                'product_id': str(product_id),
                'asin': str(latest_row['asin']),
                'sku': str(latest_row['sku']),
                'marketplace_id': str(latest_row['order_marketplace_id']),
                'customer_id': convert_to_python_type(latest_row['customer_id']),
                'recommended_action': str(action),
                'probability_demand_increase': float(round(prediction, 4)),
                'business_rule_applied': business_rule_applied,
                'days_since_last_order': int(days_since_last_order),
                'max_days_for_raise': int(self.config.MAX_DAYS_FOR_PRICE_RAISE),
                'training_loss': float(round(training_results['final_loss'], 6)),
                'epochs_trained': int(training_results['epochs_trained']),
                'last_unit_price': float(round(latest_row['unit_price'], 2)),
                'last_total_quantity': int(convert_to_python_type(latest_row['total_quantity'])),
                'last_total_price': float(round(latest_row['total_price'], 2)),
                'last_day_sold': int(convert_to_python_type(latest_row['last_day_sold'])),
                'last_quantity_ma': float(round(latest_row['quantity_ma'], 2)),
                'last_price_ma': float(round(latest_row['price_ma'], 2)),
                'last_price_change': float(round(latest_row['price_change'], 4)),
                'last_demand_change': float(round(latest_row['demand_change'], 4)),
                'last_ped': float(round(latest_row['PED'], 4)),
                'price_volatility': float(round(latest_row['price_volatility'], 4)),
                'demand_trend': int(convert_to_python_type(latest_row['demand_trend'])),
                'target_actual': int(convert_to_python_type(latest_row['target']))
            }

            logger.info(f"Product {product_id}: {action} (Probability: {prediction:.4f})")
            return recommendation

        except Exception as e:
            logger.error(f"Error processing product {product_id}: {str(e)}")
            return None

#class

def load_data_from_csv(file_path: str) -> pd.DataFrame:
    logger.info(f"Loading data from {file_path}")

    # Load data with error handling
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Data file not found: {file_path}")

    return pd.read_csv(file_path, parse_dates=['order_purchase_date'])


def predict_recommendations(data: pd.DataFrame):
    """Main function to run the improved price prediction model"""
    try:
        # Initialize model
        config = ModelConfig()
        model = PricePredictionModel(config)

        # Preprocess data
        data = model.preprocess_data(data)

        # Process each product group
        recommendations = []

        for product_id, df_group in data.groupby('product_id'):
            recommendation = model.process_product_group(df_group, product_id)
            if recommendation:
                recommendations.append(recommendation)

        # Save results
        if recommendations:
            # recommendations_df = pd.DataFrame(recommendations)

            # # Print summary statistics
            # action_counts = recommendations_df['recommended_action'].value_counts()
            # logger.info(f"Action distribution: {dict(action_counts)}")

            # avg_probability = recommendations_df['probability_demand_increase'].mean()
            # logger.info(f"Average demand increase probability: {avg_probability:.4f}")

            # print("\n" + "="*50)
            # print("PRICING RECOMMENDATIONS SUMMARY")
            # print("="*50)
            # print(recommendations_df[['product_id', 'recommended_action',
            #                         'probability_demand_increase', 'last_unit_price']].head(10))

            # return recommendations_df
            return recommendations
        else:
            logger.warning("No recommendations generated")
            return None

    except Exception as e:
        logger.error(f"Error in predict_recommendations execution: {str(e)}")
        raise
#def

def generate_recommendations_for_customer(customer_id:int, history_days:int=10):
    loader = Loader.new_instance()
    print('customer_id=', customer_id)

    if history_days < 1:
        history_days = 10

    data = []
    for new_data in loader.load_customer_data(customer_id, datetime.now() - timedelta(days=history_days), datetime.now()):
        data.extend(new_data)
    # for
    df = pd.DataFrame(data)

    # Run the model
    recommendations = predict_recommendations(df)

    if recommendations is not None:
        logger.info(f"✅ Generated {len(recommendations)} recommendations for customer {customer_id}")

        repo = ElasticPriceRepository.new_instance()
        repo.save_recommendations(recommendations)
        # repo.remove_old_recommendations(customer_id, 10)

        logger.info(f"📊 Results saved to db for customer {customer_id}")
    # if
#def

def main():
    """Main entry point for the script"""
    try:
        # Load data
        loader = Loader.new_instance()
        print('Loading data...')
        print('Start date:', datetime.now() - timedelta(days=10))
        print('End date:', datetime.now())
        customer_ids = loader.get_customer_ids_by_strategy()

        print('Number of customers:', len(customer_ids))

        for customer_id in customer_ids:
            generate_recommendations_for_customer(customer_id, 10)
        # for
        print('Done.')
    except Exception as e:
        logger.error(f"Error in main execution: {str(e)}")
        raise
#def

def main_local():
    """Main entry point for the script"""
    try:
        # Load data
        data = load_data_from_csv('sales_history10.csv')

        # Run the model
        recommendations = predict_recommendations(data)

        if recommendations is not None:
            # Save results
            output_file = 'pricing_recommendations_improved.csv'
            recommendations_df = pd.DataFrame(recommendations)
            recommendations_df.to_csv(output_file, index=False)

            logger.info(f"✅ Generated {len(recommendations)} recommendations")
            logger.info(f"📊 Results saved to {output_file}")

    except Exception as e:
        logger.error(f"Error in main execution: {str(e)}")
        raise


if __name__ == "__main__":
    main()
    # main_local()
