#!/usr/bin/env python3

from typing import List
from domains.elasticprice.loader import Loader
from domains.repository.eprice_repository import ElasticPriceRepository
from datetime import datetime, time, timedelta, timezone


class SalesHistory:

    def __init__(self, loader: Loader, eprice_repo: ElasticPriceRepository):
        self._loader = loader
        self._eprice_repo = eprice_repo
    #def

    @staticmethod
    def new_instance():
        return SalesHistory(Loader.new_instance(), ElasticPriceRepository.new_instance())
    #def

    
    def make_snapshot(self, customer_id:int, start_date:datetime, end_date:datetime):

        if customer_id > 0:
            for data in self._loader.load_customer_data(customer_id, start_date, end_date) :
                print('len(data)=', len(data))
                self._eprice_repo.save_sales_history(data)
        else:
            for data in self._loader.load_data(start_date, end_date) :
                print('len(data)=', len(data))
                self._eprice_repo.save_sales_history(data)
        
    #def
#class
