#!/usr/bin/env python3

from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Set
import pandas as pd
from domains.models.product_optimization_snapshot import ProductOptimizationSnapshot
from domains.models.recommendation import RecommendationModel
from domains.models.trained_model import TrainedModel
from facades.elasticpricedb.db import ElasticPriceDbFacade
from lib.json import json_encode

class ElasticPriceRepository:
    """Class that encapsulates data loading functions for elastic price system."""
    
    def __init__(self, db: ElasticPriceDbFacade):
        self._db = db
    #def

    @staticmethod
    def new_instance():
        return ElasticPriceRepository(ElasticPriceDbFacade.instance())
    #def


    def get_last_active_trained_model(self, model_name: str)->Optional[TrainedModel]:
        row = self._db.db_query() \
            .query_one("SELECT * FROM elastic_price.trained_model WHERE active = true AND model_name = %s ORDER BY id DESC LIMIT 1", (model_name,))
        
        if row is None:
            return None
        
        return TrainedModel.from_dict(row)
    #def


    def store_trained_model(self, model_name: str, model_dump: Any, model_info: Dict[str, Any],
                            start_time: datetime, end_time: datetime, 
                            loss: Optional[float] = None, accuracy: Optional[float] = None, 
                            training_data_size: Optional[int] = None, validation_data_size: Optional[int] = None) -> None:    
        query = self._db.db_query()
        model_dump_blob =  query.prepare_binary(model_dump)
        active = True

        query.execute(
            "INSERT INTO elastic_price.trained_model "
            "(model_name, active, loss, accuracy, model_dump, model_info, training_data_size, validation_data_size, start_time, end_time, created_at) " 
            "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", 
            ( model_name, active, loss, accuracy, model_dump_blob, json_encode(model_info), training_data_size, validation_data_size, start_time, end_time, datetime.now())
        )
    #def

    
    def store_trained_model_from_file(self, model_name: str, model_path: Path, model_info: Optional[Dict[str, Any]], 
                                      start_time: datetime, end_time: datetime, 
                                      loss: Optional[float] = None, accuracy: Optional[float]=None, 
                                      training_data_size: Optional[int] = None, validation_data_size: Optional[int] = None) -> bool:
        if model_path.exists() and model_path.is_file():
            with open(model_path, "rb") as f:
                model_dump = f.read()

                self.store_trained_model(model_name, model_dump, model_info, start_time, end_time, loss, accuracy, training_data_size, validation_data_size)   
            return True
        return False
    #def


    def save_data_history(self, data_list:List[dict]):

        sql = "INSERT INTO elastic_price.data_train_history (customer_id, order_marketplace_id, asin, order_purchase_date, version, data_json) VALUES (%s, %s, %s, %s, %s, %s)"
        trx = self._db.db_transaction()

        trx.begin()
        for row in data_list:
            trx.execute(sql, (row['customer_id'], row['order_marketplace_id'], row['asin'], row['order_purchase_date'], row['version'], json_encode(row)), raise_exception=False)
        #for
        trx.commit()
    #def
    
    
    def load_data_history(self, start_date: datetime, end_date: datetime, limit:int=100000) -> Optional[pd.DataFrame]:
        """
        Load and process all data for a customer in a specific time period.
        
        Args:
            customer_id: The ID of the customer to load data for
            history_start_date: Start date for historical data
            history_end_date: End date for historical data
            
        Returns:
            DataFrame containing all processed customer data, or None if no data is available
        """
        sql = "SELECT * FROM elastic_price.data_train_history WHERE order_purchase_date >= %s AND order_purchase_date < %s LIMIT %s"
        data = self._db.db_query().query_all(sql, (start_date, end_date, limit))
        
        if len(data) == 0:
            return None
        
        items = []
        for row in data:
            items.append(row['data_json'])

        if len(items) == 0:
            return None
        
        df = pd.DataFrame(items)
        
        return df
        
    #def

    def store_product_optimization_snapshot(self, customer_id: int, optimization_data: List[Dict[str, Any]], start_date: datetime, end_date: datetime) -> None:
        sql = "INSERT INTO elastic_price.product_optimization_snapshot (customer_id, snapshot_date_start, snapshot_date_end, amount, data_json) VALUES (%s, %s, %s, %s, %s)"
        self._db.db_query().execute(sql, (customer_id, start_date, end_date, len(optimization_data), json_encode(optimization_data)))
    #def

    def get_last_product_optimization_snapshot(self, customer_id: int) -> Optional[Dict[str, Any]]:
        sql = "SELECT * FROM elastic_price.product_optimization_snapshot WHERE customer_id = %s ORDER BY id DESC LIMIT 1"

        data = self._db.db_query().query_one(sql, (customer_id,))
        return data
    #def
    
    def delete_product_optimization_snapshot(self, id: int) -> None:
        sql = "DELETE FROM elastic_price.product_optimization_snapshot WHERE id = %s"
        self._db.db_query().execute(sql, (id,))
    #def

    def get_product_optimization_snapshots(self, customer_id: int, start_date: datetime) -> List[ProductOptimizationSnapshot]:
        sql = "SELECT * FROM elastic_price.product_optimization_snapshot WHERE customer_id = %s AND snapshot_date_start >= %s ORDER BY id ASC"
        items = self._db.db_query().query_all(sql, (customer_id, start_date))

        return ProductOptimizationSnapshot.from_dicts(items)
    #def


    def save_sales_history(self, sales_list:List[dict]):
        if len(sales_list) == 0:
            return

        sql = """
            INSERT INTO elastic_price.sales_history 
            ( asin, sku, marketplace_id, order_purchase_date, customer_id, total_orders, total_quantity, total_price) 
              VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
              ON CONFLICT (asin, sku, marketplace_id, order_purchase_date, customer_id) 
              do UPDATE SET total_orders = EXCLUDED.total_orders, total_quantity = EXCLUDED.total_quantity, total_price = EXCLUDED.total_price
        """
        trx = self._db.db_query()

        # trx.begin()
        # for row in sales_list:
        #     trx.execute(sql, (row['asin'], row['sku'], row['marketplace_id'], row['order_purchase_date'], row['customer_id'], row['total_orders'], row['total_quantity'], row['total_price']), raise_exception=False)
        # #for
        # trx.commit()
        for row in sales_list:
            # print(row)
            trx.execute(sql, (row['asin'], row['sku'], row['order_marketplace_id'], row['order_purchase_date'], row['customer_id'], row['total_orders'], row['total_quantity'], row['total_price']))
        #for
    #def


    def save_recommendations(self, recommendations:list[dict[str, Any]]):
        if len(recommendations) == 0:
            return


        sql = """
            INSERT INTO elastic_price.recommendation 
            (customer_id, asin, sku, marketplace_id, recommended_action, probability_demand_increase, training_loss,  recommendation_info, modified_at) 
              VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
              ON CONFLICT (customer_id, asin, sku, marketplace_id) 
              do UPDATE SET 
                recommended_action = EXCLUDED.recommended_action, 
                probability_demand_increase = EXCLUDED.probability_demand_increase, 
                training_loss = EXCLUDED.training_loss, 
                recommendation_info = EXCLUDED.recommendation_info, 
                modified_at = EXCLUDED.modified_at
        """
        trx = self._db.db_query()

        now = datetime.now()
        for row in recommendations:
            recommendation_info = json_encode(row)
            trx.execute(sql, (row['customer_id'], row['asin'], row['sku'], row['marketplace_id'], row['recommended_action'], row['probability_demand_increase'], row['training_loss'], recommendation_info, now))
        #for
    #def

    def get_recommendation(self, customer_id: int, asin: str, sku: str, marketplace_id: str) -> Optional[RecommendationModel]:
        sql = "SELECT * FROM elastic_price.recommendation WHERE customer_id = %s AND asin = %s AND sku = %s AND marketplace_id = %s LIMIT 1"
        row = self._db.db_query().query_one(sql, (customer_id, asin, sku, marketplace_id))
        if row is None:
            return None

        return RecommendationModel.from_dict(row)
    #def
    
    def remove_old_recommendations(self, customer_id: int, days: int) -> None:
        sql = "DELETE FROM elastic_price.recommendation WHERE customer_id = %s AND modified_at < NOW() - INTERVAL '%s' DAY"
        self._db.db_query().execute(sql, (customer_id, days))
    #def

#class

