Metadata-Version: 2.4
Name: elastic_price
Version: 0.2.0
Summary: Elastic pricing model for dynamic price optimization using machine learning
Home-page: https://gitlab.sl.local/sellerlogic/elastic-price
Author: <PERSON><PERSON><PERSON>
Author-email: <PERSON><PERSON><PERSON> <v.t<PERSON><PERSON><PERSON><PERSON>@sellerlogic.com>
License-Expression: MIT
Project-URL: Homepage, https://gitlab.sl.local/sellerlogic/elastic-price
Project-URL: Repository, https://gitlab.sl.local/sellerlogic/elastic-price
Project-URL: Issues, https://gitlab.sl.local/sellerlogic/elastic-price/-/issues
Project-URL: Documentation, https://gitlab.sl.local/sellerlogic/elastic-price/-/blob/master/README.md
Keywords: api,cli,python,pricing,optimization,machine learning,pytorch,fastapi,neural networks
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: Topic :: Office/Business :: Financial
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Operating System :: OS Independent
Classifier: Framework :: FastAPI
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Requires-Python: >=3.12.0
Description-Content-Type: text/markdown
Requires-Dist: fastapi[standard]>=0.115.12
Requires-Dist: uvicorn>=0.34.2
Requires-Dist: gunicorn>=23.0.0
Requires-Dist: pydantic>=2.11.3
Requires-Dist: pydantic-settings>=2.9.1
Requires-Dist: python-dotenv>=1.1.0
Requires-Dist: python-multipart>=0.0.20
Requires-Dist: click>=8.1.8
Requires-Dist: typer>=0.15.3
Requires-Dist: rich>=14.0.0
Requires-Dist: pandas>=2.2.3
Requires-Dist: numpy>=2.2.5
Requires-Dist: scikit-learn>=1.6.1
Requires-Dist: seaborn>=0.13.2
Requires-Dist: matplotlib>=3.10.1
Requires-Dist: scipy>=1.15.2
Requires-Dist: statsmodels>=0.14.4
Requires-Dist: torch>=2.7.0
Requires-Dist: torchvision>=0.22.0
Requires-Dist: torchaudio>=0.22.0
Requires-Dist: clickhouse-connect>=0.8.17
Requires-Dist: PyMySQL>=1.1.1
Requires-Dist: redis>=5.2.1
Requires-Dist: psycopg[binary]>=3.2.6
Requires-Dist: cryptography>=44.0.3
Requires-Dist: PyYAML>=6.0.2
Requires-Dist: python-dateutil>=2.9.0
Provides-Extra: dev
Requires-Dist: pytest>=7.4.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: black>=24.1.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: mypy>=1.5.1; extra == "dev"
Requires-Dist: flake8>=6.1.0; extra == "dev"
Requires-Dist: pre-commit>=3.0.0; extra == "dev"
Provides-Extra: jupyter
Requires-Dist: jupyterlab>=4.0.0; extra == "jupyter"
Requires-Dist: ipykernel>=6.29.5; extra == "jupyter"
Requires-Dist: ipython>=9.2.0; extra == "jupyter"
Requires-Dist: notebook>=7.0.0; extra == "jupyter"
Provides-Extra: all
Requires-Dist: pytest>=7.4.0; extra == "all"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "all"
Requires-Dist: black>=24.1.0; extra == "all"
Requires-Dist: isort>=5.12.0; extra == "all"
Requires-Dist: mypy>=1.5.1; extra == "all"
Requires-Dist: flake8>=6.1.0; extra == "all"
Requires-Dist: pre-commit>=3.0.0; extra == "all"
Requires-Dist: jupyterlab>=4.0.0; extra == "all"
Requires-Dist: ipykernel>=6.29.5; extra == "all"
Requires-Dist: ipython>=9.2.0; extra == "all"
Requires-Dist: notebook>=7.0.0; extra == "all"
Dynamic: author
Dynamic: home-page
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# elastic-price

```bash
source .venv/bin/activate
```

API SERVER RUN

```bash
uvicorn app:app --reload --app-dir='api'
```

CLI

```bash
python3 -m cli.ml elastic-price train

python3 -m cli.ml elastic-price retrain --model=ep2 --new-model=0 --snapshot-days=100
```
